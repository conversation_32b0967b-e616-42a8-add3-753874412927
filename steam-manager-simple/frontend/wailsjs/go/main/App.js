// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddAccount(arg1, arg2, arg3) {
  return window['go']['main']['App']['AddAccount'](arg1, arg2, arg3);
}

export function AddAccountWithoutSteamID(arg1, arg2, arg3) {
  return window['go']['main']['App']['AddAccountWithoutSteamID'](arg1, arg2, arg3);
}

export function CheckAccountBanStatus(arg1) {
  return window['go']['main']['App']['CheckAccountBanStatus'](arg1);
}

export function ClearLogs() {
  return window['go']['main']['App']['ClearLogs']();
}

export function DeleteAccount(arg1) {
  return window['go']['main']['App']['DeleteAccount'](arg1);
}

export function GetAccountStats() {
  return window['go']['main']['App']['GetAccountStats']();
}

export function GetAccounts() {
  return window['go']['main']['App']['GetAccounts']();
}

export function GetAccountsPaginated(arg1, arg2) {
  return window['go']['main']['App']['GetAccountsPaginated'](arg1, arg2);
}

export function GetConfig() {
  return window['go']['main']['App']['GetConfig']();
}

export function GetInstalledSteamGames() {
  return window['go']['main']['App']['GetInstalledSteamGames']();
}

export function GetLogs() {
  return window['go']['main']['App']['GetLogs']();
}

export function Greet(arg1) {
  return window['go']['main']['App']['Greet'](arg1);
}

export function ImportAccounts(arg1) {
  return window['go']['main']['App']['ImportAccounts'](arg1);
}

export function LaunchGame() {
  return window['go']['main']['App']['LaunchGame']();
}

export function LoginSteam(arg1, arg2) {
  return window['go']['main']['App']['LoginSteam'](arg1, arg2);
}

export function ParseImportLine(arg1) {
  return window['go']['main']['App']['ParseImportLine'](arg1);
}

export function ResolveSteamID(arg1) {
  return window['go']['main']['App']['ResolveSteamID'](arg1);
}

export function SetAccountSteamID(arg1, arg2) {
  return window['go']['main']['App']['SetAccountSteamID'](arg1, arg2);
}

export function TriggerSteamIDCheck() {
  return window['go']['main']['App']['TriggerSteamIDCheck']();
}

export function UpdateAccount(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['UpdateAccount'](arg1, arg2, arg3, arg4);
}

export function UpdateConfig(arg1, arg2, arg3, arg4, arg5, arg6) {
  return window['go']['main']['App']['UpdateConfig'](arg1, arg2, arg3, arg4, arg5, arg6);
}
