package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"time"
)

// LoginSteam Steam登录
func (a *App) LoginSteam(username, password string) error {
	// 简单验证
	if username == "" || password == "" {
		return fmt.Errorf("用户名和密码不能为空")
	}

	// 启动Steam登录
	err := a.launchSteamWithAccount(username, password)
	if err != nil {
		return err
	}

	// 更新账号登录状态
	go func() {
		// 等待Steam启动完成
		time.Sleep(3 * time.Second)
		a.updateAccountLastLogin(username)
	}()

	return nil
}

// launchSteamWithAccount 启动Steam并登录指定账号
func (a *App) launchSteamWithAccount(username, password string) error {
	// 如果配置了自动关闭游戏，先关闭游戏
	if a.config.Steam.GameConfig.AutoCloseGame {
		a.addLog("INFO", "正在关闭现有游戏进程...", "general")
		if err := a.closePUBG(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭游戏失败: %v", err), "general")
		}
	}

	// 检查Steam是否正在运行
	if a.isSteamRunning() {
		// 如果Steam正在运行，先关闭它
		a.addLog("INFO", "正在关闭现有Steam进程...", "general")
		if err := a.closeSteam(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭Steam失败: %v", err), "general")
		}
		// 等待Steam完全关闭
		time.Sleep(3 * time.Second)
	}

	// 启动Steam并登录
	return a.startSteamWithLogin(username, password)
}

// isSteamRunning 检查Steam是否正在运行
func (a *App) isSteamRunning() bool {
	// 使用tasklist命令检查Steam进程
	result, err := a.executeCommand("tasklist", "/FI", "IMAGENAME eq Steam.exe")
	if err != nil {
		return false
	}

	// 检查输出中是否包含Steam.exe
	return strings.Contains(strings.ToLower(result), "steam.exe")
}

// closeSteam 关闭Steam
func (a *App) closeSteam() error {
	// 使用taskkill命令关闭Steam
	cmd := `taskkill /F /IM Steam.exe`
	_, err := a.executeCommand("cmd", "/C", cmd)
	return err
}

// isGameRunning 检查指定游戏进程是否正在运行
func (a *App) isGameRunning(processName string) bool {
	result, err := a.executeCommand("tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s", processName))
	if err != nil {
		return false
	}
	// 检查输出中是否包含进程名
	return strings.Contains(strings.ToLower(result), strings.ToLower(processName))
}

// isPUBGRunning 检查PUBG是否正在运行
func (a *App) isPUBGRunning() bool {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	for _, processName := range pubgProcesses {
		if a.isGameRunning(processName) {
			return true
		}
	}
	return false
}

// closeGame 关闭指定的游戏进程
func (a *App) closeGame(processName string) error {
	if !a.isGameRunning(processName) {
		return nil // 游戏没有运行，无需关闭
	}

	// 尝试优雅关闭
	cmd := fmt.Sprintf(`taskkill /IM %s`, processName)
	_, err := a.executeCommand("cmd", "/C", cmd)
	if err == nil {
		// 等待进程关闭
		time.Sleep(3 * time.Second)
		if !a.isGameRunning(processName) {
			return nil
		}
	}

	// 强制关闭
	cmd = fmt.Sprintf(`taskkill /F /IM %s`, processName)
	_, err = a.executeCommand("cmd", "/C", cmd)
	return err
}

// closePUBG 关闭PUBG游戏
func (a *App) closePUBG() error {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	var lastErr error
	for _, processName := range pubgProcesses {
		err := a.closeGame(processName)
		if err != nil {
			lastErr = err
			a.addLog("ERROR", fmt.Sprintf("关闭 %s 失败: %v", processName, err), "general")
		} else if a.isGameRunning(processName) {
			a.addLog("SUCCESS", fmt.Sprintf("成功关闭 %s", processName), "general")
		}
	}

	return lastErr
}

// startSteamWithLogin 启动Steam并登录
func (a *App) startSteamWithLogin(username, password string) error {
	// 查找Steam安装路径
	steamPath := a.findSteamPath()
	if steamPath == "" {
		return fmt.Errorf("未找到Steam安装路径")
	}

	// 构建Steam启动命令
	steamExe := filepath.Join(steamPath, "Steam.exe")

	// 启动Steam（不等待完成，隐藏窗口）
	cmd := exec.Command(steamExe, "-login", username, password)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动Steam失败: %v", err), "general")
		return fmt.Errorf("启动Steam失败: %v", err)
	}

	a.addLog("SUCCESS", fmt.Sprintf("Steam启动命令已执行: %s -login %s ***", steamExe, username), "general")

	// 等待Steam启动
	time.Sleep(5 * time.Second)

	// 如果配置了自动启动游戏，则启动游戏
	if a.config.Steam.AutoLaunchGame {
		go func() {
			// 等待Steam完全启动
			time.Sleep(10 * time.Second)

			// 如果启用了安妮程序，先启动安妮程序
			annieSuccess := false
			if a.config.Annie.Enabled {
				a.addLog("INFO", "Steam登录成功，开始启动安妮程序", "general")
				if err := a.launchAnnie(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动安妮程序失败: %v", err), "general")
					a.addLog("INFO", "安妮程序启动失败，跳过游戏启动", "general")
					return // 安妮程序启动失败，不启动游戏
				} else {
					// 安妮程序启动成功，等待其处理完成后再启动游戏
					a.addLog("INFO", "安妮程序启动成功，等待处理完成后启动游戏", "general")
					// 等待并处理弹窗、进程退出和文件消失
					if err := a.handleAnnieDialogs(); err != nil {
						a.addLog("ERROR", fmt.Sprintf("安妮程序处理失败: %v", err), "general")
						a.addLog("INFO", "安妮程序处理失败，跳过游戏启动", "general")
						return // 安妮程序处理失败，不启动游戏
					}
					annieSuccess = true
				}
			}

			// 只有在安妮程序未启用或处理完成后才启动游戏
			if !a.config.Annie.Enabled || annieSuccess {
				if err := a.launchGame(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
				}
			}
		}()
	}

	return nil
}

// findSteamPath 查找Steam安装路径
func (a *App) findSteamPath() string {
	// 常见的Steam安装路径
	commonPaths := []string{
		`C:\Program Files (x86)\Steam`,
		`C:\Program Files\Steam`,
		`D:\Steam`,
		`E:\Steam`,
		`F:\Steam`,
	}

	// 检查常见路径
	for _, path := range commonPaths {
		steamExe := filepath.Join(path, "Steam.exe")
		if _, err := os.Stat(steamExe); err == nil {
			return path
		}
	}

	// 尝试从注册表读取
	steamPath := a.getSteamPathFromRegistry()
	if steamPath != "" {
		return steamPath
	}

	return ""
}

// getSteamPathFromRegistry 从注册表获取Steam路径
func (a *App) getSteamPathFromRegistry() string {
	// 使用reg命令查询注册表
	cmd := `reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Valve\Steam" /v InstallPath 2>NUL`
	result, err := a.executeCommand("cmd", "/C", cmd)
	if err != nil {
		return ""
	}

	// 解析注册表输出
	lines := strings.Split(result, "\n")
	for _, line := range lines {
		if strings.Contains(line, "InstallPath") && strings.Contains(line, "REG_SZ") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				return strings.Join(parts[2:], " ")
			}
		}
	}

	return ""
}

// launchGame 启动游戏
func (a *App) launchGame() error {
	gameID := a.config.Steam.GameConfig.GameID
	if gameID == "" {
		gameID = "578080" // PUBG的Steam ID
	}

	// 使用Steam URL协议启动游戏
	steamURL := fmt.Sprintf("steam://rungameid/%s", gameID)

	// 使用start命令启动URL（隐藏窗口）
	cmd := exec.Command("cmd", "/C", "start", steamURL)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
		return err
	}

	a.addLog("SUCCESS", fmt.Sprintf("游戏启动命令已执行: %s", steamURL), "general")
	return nil
}

// LaunchGame 公开的启动游戏方法
func (a *App) LaunchGame() error {
	return a.launchGame()
}

// GetInstalledSteamGames 获取本地已安装的Steam游戏列表
func (a *App) GetInstalledSteamGames() ([]SteamGame, error) {
	steamPath := a.findSteamPath()
	if steamPath == "" {
		a.addLog("ERROR", "未找到Steam安装路径", "general")
		return nil, fmt.Errorf("未找到Steam安装路径")
	}

	a.addLog("INFO", fmt.Sprintf("找到Steam安装路径: %s", steamPath), "general")
	games := []SteamGame{}

	// 添加默认的PUBG游戏
	games = append(games, SteamGame{
		AppID: "578080",
		Name:  "PLAYERUNKNOWN'S BATTLEGROUNDS",
	})

	// 获取所有Steam库路径
	libraryPaths := a.getSteamLibraryPaths(steamPath)
	a.addLog("INFO", fmt.Sprintf("找到 %d 个Steam库路径", len(libraryPaths)), "general")

	for _, libraryPath := range libraryPaths {
		a.addLog("INFO", fmt.Sprintf("扫描Steam库: %s", libraryPath), "general")
		libraryGames := a.scanSteamLibrary(libraryPath)

		for _, game := range libraryGames {
			// 过滤掉一些非游戏的应用（如Steam工具、DLC等）
			if a.isValidGame(game) {
				games = append(games, game)
				a.addLog("INFO", fmt.Sprintf("添加有效游戏: %s (%s)", game.Name, game.AppID), "general")
			} else {
				a.addLog("INFO", fmt.Sprintf("过滤掉非游戏应用: %s (%s)", game.Name, game.AppID), "general")
			}
		}
	}

	a.addLog("INFO", fmt.Sprintf("最终找到 %d 个已安装的Steam游戏", len(games)), "general")
	return games, nil
}

// parseACFFile 解析ACF文件获取游戏信息
func (a *App) parseACFFile(filePath string) (SteamGame, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return SteamGame{}, err
	}

	contentStr := string(content)
	game := SteamGame{}

	// 使用正则表达式或更简单的字符串解析
	lines := strings.Split(contentStr, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 解析AppID
		if strings.Contains(line, `"appid"`) && strings.Contains(line, `"`) {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				game.AppID = parts[3]
			}
		}

		// 解析游戏名称
		if strings.Contains(line, `"name"`) && strings.Contains(line, `"`) {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				game.Name = parts[3]
			}
		}

		// 如果两个都找到了，可以提前退出
		if game.AppID != "" && game.Name != "" {
			break
		}
	}

	if game.AppID == "" || game.Name == "" {
		return SteamGame{}, fmt.Errorf("无法解析游戏信息: AppID=%s, Name=%s", game.AppID, game.Name)
	}

	return game, nil
}

// extractValueFromACF 从ACF内容中提取值
func (a *App) extractValueFromACF(content string) string {
	lines := strings.Split(content, "\n")
	if len(lines) == 0 {
		return ""
	}

	// 找到包含值的行
	firstLine := strings.TrimSpace(lines[0])
	if strings.Contains(firstLine, `"`) {
		// 提取引号中的值
		parts := strings.Split(firstLine, `"`)
		if len(parts) >= 4 {
			return parts[3] // 第二个引号对中的内容
		}
	}

	return ""
}

// isValidGame 判断是否为有效的游戏（过滤工具、DLC等）
func (a *App) isValidGame(game SteamGame) bool {
	// 过滤掉一些明显的非游戏应用
	excludeKeywords := []string{
		"Steamworks Common Redistributables",
		"Steam Client",
		"Proton",
		"DirectX",
		"Visual C++",
		"Microsoft",
	}

	gameName := strings.ToLower(game.Name)
	for _, keyword := range excludeKeywords {
		if strings.Contains(gameName, strings.ToLower(keyword)) {
			a.addLog("INFO", fmt.Sprintf("过滤关键词匹配: %s 包含 %s", game.Name, keyword), "general")
			return false
		}
	}

	// AppID过滤：一些特殊的AppID范围通常不是游戏
	// Steam工具类应用通常AppID较小
	if game.AppID == "228980" || // Steamworks Common Redistributables
		game.AppID == "1007" ||  // Steam Client Bootstrapper
		game.AppID == "7" {      // Steam Client
		a.addLog("INFO", fmt.Sprintf("过滤特殊AppID: %s (%s)", game.Name, game.AppID), "general")
		return false
	}

	// 跳过重复的PUBG（因为我们已经默认添加了）
	if game.AppID == "578080" {
		a.addLog("INFO", fmt.Sprintf("跳过重复的PUBG: %s (%s)", game.Name, game.AppID), "general")
		return false
	}

	return true
}
