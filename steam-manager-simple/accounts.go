package main

import (
	"fmt"
	"strings"
	"time"
)

// GetAccounts 获取所有账号
func (a *App) GetAccounts() []Account {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	// 返回账号副本
	accounts := make([]Account, len(a.accounts))
	copy(accounts, a.accounts)
	return accounts
}

// GetAccountsPaginated 分页获取账号
func (a *App) GetAccountsPaginated(page, pageSize int) (*AccountListResponse, error) {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	total := len(a.accounts)
	totalPages := (total + pageSize - 1) / pageSize

	if total == 0 {
		return &AccountListResponse{
			Accounts:    []Account{},
			Total:       0,
			CurrentPage: page,
			TotalPages:  0,
			PageSize:    pageSize,
		}, nil
	}

	start := (page - 1) * pageSize
	if start >= total {
		return &AccountListResponse{
			Accounts:    []Account{},
			Total:       total,
			CurrentPage: page,
			TotalPages:  totalPages,
			PageSize:    pageSize,
		}, nil
	}

	end := start + pageSize
	if end > total {
		end = total
	}

	return &AccountListResponse{
		Accounts:    a.accounts[start:end],
		Total:       total,
		CurrentPage: page,
		TotalPages:  totalPages,
		PageSize:    pageSize,
	}, nil
}

// GetAccountStats 获取账号统计
func (a *App) GetAccountStats() map[string]int {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	stats := map[string]int{
		"total":   len(a.accounts),
		"normal":  0,
		"banned":  0,
		"unknown": 0,
	}

	for _, account := range a.accounts {
		switch account.Status {
		case "正常":
			stats["normal"]++
		case "VAC封禁":
			stats["banned"]++
		case "游戏封禁":
			stats["banned"]++
		default:
			if strings.Contains(account.Status, "游戏封禁") {
				stats["banned"]++
			} else {
				stats["unknown"]++
			}
		}
	}

	return stats
}

// AddAccount 添加账号
func (a *App) AddAccount(username, password, notes string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 检查用户名是否已存在
	for _, account := range a.accounts {
		if account.Username == username {
			return fmt.Errorf("用户名 %s 已存在", username)
		}
	}

	now := time.Now()
	newAccount := Account{
		Username:      username,
		Password:      password,
		Notes:         notes,
		Status:        "未知",
		SteamID:       "",
		PUBGBanStatus: -1,
		IsLoggedIn:    false,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// 如果有数据库，保存到数据库
	if a.db != nil {
		result, err := a.db.Exec(`
			INSERT INTO accounts (username, password, steam_id, pubg_ban_status, notes, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?)
		`, username, password, "", -1, notes, now, now)

		if err != nil {
			return fmt.Errorf("failed to save account to database: %v", err)
		}

		id, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("failed to get account ID: %v", err)
		}
		newAccount.ID = int(id)
	} else {
		// 内存存储
		newAccount.ID = a.nextID
		a.nextID++
	}

	// 插入账号并保持ID排序
	a.insertAccountSorted(newAccount)

	// 后台获取SteamID
	go a.fetchSteamIDBackground(username, password)

	return nil
}

// AddAccountWithoutSteamID 添加账号但不获取SteamID（用于批量导入）
func (a *App) AddAccountWithoutSteamID(username, password, notes string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 检查用户名是否已存在
	for _, account := range a.accounts {
		if account.Username == username {
			return fmt.Errorf("用户名 %s 已存在", username)
		}
	}

	now := time.Now()
	newAccount := Account{
		Username:      username,
		Password:      password,
		Notes:         notes,
		Status:        "未知",
		SteamID:       "",
		PUBGBanStatus: -1,
		IsLoggedIn:    false,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// 如果有数据库，保存到数据库
	if a.db != nil {
		result, err := a.db.Exec(`
			INSERT INTO accounts (username, password, steam_id, pubg_ban_status, notes, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?)
		`, username, password, "", -1, notes, now, now)

		if err != nil {
			return fmt.Errorf("failed to save account to database: %v", err)
		}

		id, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("failed to get account ID: %v", err)
		}
		newAccount.ID = int(id)
	} else {
		// 内存存储
		newAccount.ID = a.nextID
		a.nextID++
	}

	// 插入账号并保持ID排序
	a.insertAccountSorted(newAccount)
	return nil
}

// UpdateAccount 更新账号
func (a *App) UpdateAccount(id int, username, password, notes string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 检查新用户名是否与其他账号冲突
	for _, account := range a.accounts {
		if account.ID != id && account.Username == username {
			return fmt.Errorf("用户名 %s 已存在", username)
		}
	}

	// 更新数据库
	if a.db != nil {
		result, err := a.db.Exec(`
			UPDATE accounts SET username = ?, password = ?, notes = ?, updated_at = ? WHERE id = ?
		`, username, password, notes, time.Now(), id)

		if err != nil {
			return fmt.Errorf("failed to update account in database: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %v", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("账号 ID %d 不存在", id)
		}
	}

	// 更新内存
	for i, account := range a.accounts {
		if account.ID == id {
			a.accounts[i].Username = username
			a.accounts[i].Password = password
			a.accounts[i].Notes = notes
			a.accounts[i].UpdatedAt = time.Now()
			return nil
		}
	}

	return fmt.Errorf("账号 ID %d 不存在", id)
}

// DeleteAccount 删除账号
func (a *App) DeleteAccount(id int) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 从数据库删除
	if a.db != nil {
		result, err := a.db.Exec("DELETE FROM accounts WHERE id = ?", id)
		if err != nil {
			return fmt.Errorf("failed to delete account from database: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %v", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("账号 ID %d 不存在", id)
		}
	}

	// 从内存删除
	for i, account := range a.accounts {
		if account.ID == id {
			a.accounts = append(a.accounts[:i], a.accounts[i+1:]...)
			return nil
		}
	}

	return fmt.Errorf("账号 ID %d 不存在", id)
}

// SetAccountSteamID 手动设置账号的SteamID
func (a *App) SetAccountSteamID(accountId int, steamID string) error {
	if steamID == "" {
		return fmt.Errorf("SteamID不能为空")
	}

	// 验证SteamID格式（简单验证）
	if len(steamID) != 17 || !strings.HasPrefix(steamID, "76561") {
		return fmt.Errorf("SteamID格式不正确，应该是17位数字，以76561开头")
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 更新账号的SteamID
	for i, account := range a.accounts {
		if account.ID == accountId {
			a.accounts[i].SteamID = steamID
			a.accounts[i].UpdatedAt = time.Now()

			// 更新数据库
			if a.db != nil {
				_, err := a.db.Exec(`
					UPDATE accounts SET steam_id = ?, updated_at = ? WHERE id = ?
				`, steamID, time.Now(), accountId)
				if err != nil {
					return fmt.Errorf("更新数据库失败: %v", err)
				}
			}

			// 设置SteamID后，立即检查封禁状态
			go a.checkSingleAccountBan(accountId, steamID)

			return nil
		}
	}

	return fmt.Errorf("账号不存在")
}

// insertAccountSorted 按ID顺序插入账号到内存列表
func (a *App) insertAccountSorted(newAccount Account) {
	// 如果列表为空，直接添加
	if len(a.accounts) == 0 {
		a.accounts = append(a.accounts, newAccount)
		return
	}

	// 找到插入位置（按ID升序）
	insertIndex := len(a.accounts)
	for i, account := range a.accounts {
		if newAccount.ID < account.ID {
			insertIndex = i
			break
		}
	}

	// 在指定位置插入
	if insertIndex == len(a.accounts) {
		// 插入到末尾
		a.accounts = append(a.accounts, newAccount)
	} else {
		// 插入到中间
		a.accounts = append(a.accounts[:insertIndex+1], a.accounts[insertIndex:]...)
		a.accounts[insertIndex] = newAccount
	}
}
