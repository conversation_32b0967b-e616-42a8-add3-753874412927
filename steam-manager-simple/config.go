package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

const configFileName = "config.json"

// initializeConfig 初始化配置
func (a *App) initializeConfig() error {
	// 先设置默认配置
	a.setDefaultConfig()

	// 尝试从文件加载配置
	if err := a.loadConfigFromFile(); err != nil {
		a.addLog("INFO", "未找到配置文件，使用默认配置", "general")
		// 保存默认配置到文件
		if err := a.saveConfigToFile(); err != nil {
			a.addLog("WARN", "保存默认配置失败: "+err.Error(), "general")
		}
	} else {
		a.addLog("INFO", "配置文件加载成功", "general")
	}

	return nil
}

// setDefaultConfig 设置默认配置
func (a *App) setDefaultConfig() {
	a.config = &Config{}
	a.config.Database.Path = "./data/steam_accounts.db"
	a.config.Steam.AutoLaunchGame = true
	a.config.Steam.GameConfig.GameID = "578080"
	a.config.Steam.GameConfig.GameName = "PLAYERUNKNOWN'S BATTLEGROUNDS"
	a.config.Steam.GameConfig.LaunchArgs = ""
	a.config.Steam.GameConfig.AutoCloseGame = true
	a.config.Annie.Enabled = false
	a.config.Annie.RarPassword = "aaa111"
	a.config.API.SteamAPIKeys = []string{
		"6450F125515588614814C4A636002A51",
		"5EB306084E5CB78D76E3DDFBF03346A7",
		"C1FE80472F4FA401E9BF38E195EB8677",
		"F1C8B59D43BAC59F6B648FD0D217B974",
		"738D637E98D73D27B9802CA833784D7F",
		"A862570CC2139926066420C4E9A5A927",
	}
}

// GetConfig 获取配置
func (a *App) GetConfig() *Config {
	return a.config
}

// loadConfigFromFile 从文件加载配置
func (a *App) loadConfigFromFile() error {
	configPath := filepath.Join(".", configFileName)

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return err
	}

	// 读取文件内容
	data, err := os.ReadFile(configPath)
	if err != nil {
		return err
	}

	// 解析JSON
	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return err
	}

	// 更新配置，但保留API密钥（避免覆盖内置密钥）
	a.config.Steam.AutoLaunchGame = config.Steam.AutoLaunchGame
	a.config.Steam.GameConfig.AutoCloseGame = config.Steam.GameConfig.AutoCloseGame
	a.config.Annie.Enabled = config.Annie.Enabled
	a.config.Annie.RarPassword = config.Annie.RarPassword

	// 加载游戏配置
	if config.Steam.GameConfig.GameID != "" {
		a.config.Steam.GameConfig.GameID = config.Steam.GameConfig.GameID
	}
	if config.Steam.GameConfig.GameName != "" {
		a.config.Steam.GameConfig.GameName = config.Steam.GameConfig.GameName
	}

	// 如果配置文件中有数据库路径，也加载它
	if config.Database.Path != "" {
		a.config.Database.Path = config.Database.Path
	}

	return nil
}

// saveConfigToFile 保存配置到文件
func (a *App) saveConfigToFile() error {
	configPath := filepath.Join(".", configFileName)

	// 创建一个只包含用户配置的结构
	userConfig := struct {
		Database struct {
			Path string `json:"path"`
		} `json:"database"`
		Steam struct {
			AutoLaunchGame bool `json:"auto_launch_game"`
			GameConfig     struct {
				GameID        string `json:"game_id"`
				GameName      string `json:"game_name"`
				AutoCloseGame bool   `json:"auto_close_game"`
			} `json:"game_config"`
		} `json:"steam"`
		Annie struct {
			Enabled     bool   `json:"enabled"`
			RarPassword string `json:"rar_password"`
		} `json:"annie"`
	}{}

	// 填充用户配置
	userConfig.Database.Path = a.config.Database.Path
	userConfig.Steam.AutoLaunchGame = a.config.Steam.AutoLaunchGame
	userConfig.Steam.GameConfig.GameID = a.config.Steam.GameConfig.GameID
	userConfig.Steam.GameConfig.GameName = a.config.Steam.GameConfig.GameName
	userConfig.Steam.GameConfig.AutoCloseGame = a.config.Steam.GameConfig.AutoCloseGame
	userConfig.Annie.Enabled = a.config.Annie.Enabled
	userConfig.Annie.RarPassword = a.config.Annie.RarPassword

	// 转换为JSON
	data, err := json.MarshalIndent(userConfig, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(configPath, data, 0644)
}

// UpdateConfig 更新配置
func (a *App) UpdateConfig(autoLaunchGame, autoCloseGame, annieEnabled bool, annieRarPassword, gameID, gameName string) error {
	a.config.Steam.AutoLaunchGame = autoLaunchGame
	a.config.Steam.GameConfig.AutoCloseGame = autoCloseGame
	a.config.Annie.Enabled = annieEnabled
	a.config.Annie.RarPassword = annieRarPassword

	// 更新游戏配置
	if gameID != "" {
		a.config.Steam.GameConfig.GameID = gameID
	}
	if gameName != "" {
		a.config.Steam.GameConfig.GameName = gameName
	}

	// 保存配置到文件
	if err := a.saveConfigToFile(); err != nil {
		a.addLog("ERROR", "保存配置到文件失败: "+err.Error(), "general")
		return err
	}

	a.addLog("INFO", fmt.Sprintf("配置已更新并保存到文件，当前游戏: %s (%s)", gameName, gameID), "general")
	return nil
}

// getNextAPIKey 获取下一个可用的API密钥（轮询）
func (a *App) getNextAPIKey() string {
	if len(a.config.API.SteamAPIKeys) == 0 {
		return ""
	}

	apiKey := a.config.API.SteamAPIKeys[a.apiKeyIndex]
	a.apiKeyIndex = (a.apiKeyIndex + 1) % len(a.config.API.SteamAPIKeys)

	return apiKey
}
