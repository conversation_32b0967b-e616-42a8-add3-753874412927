package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"time"
)

// CheckAccountBanStatus 手动检查账号封禁状态
func (a *App) CheckAccountBanStatus(accountId int) error {
	a.mutex.RLock()
	var account *Account
	for _, acc := range a.accounts {
		if acc.ID == accountId {
			account = &acc
			break
		}
	}
	a.mutex.RUnlock()

	if account == nil {
		return fmt.Errorf("账号不存在")
	}

	if account.SteamID == "" {
		return fmt.Errorf("账号尚未获取到SteamID")
	}

	// 启动后台检查
	go a.checkSingleAccountBan(accountId, account.SteamID)

	return nil
}

// checkBanStatusBackground 后台检查封禁状态
func (a *App) checkBanStatusBackground() {
	if len(a.config.API.SteamAPIKeys) == 0 {
		return
	}

	a.mutex.RLock()
	accountsToCheck := make([]Account, 0)

	// 筛选需要检查的账号
	for _, account := range a.accounts {
		if account.SteamID != "" && time.Since(account.LastChecked) > time.Hour {
			accountsToCheck = append(accountsToCheck, account)
		}
	}
	a.mutex.RUnlock()

	if len(accountsToCheck) == 0 {
		return
	}

	// 按最后登录时间排序，最近登录的优先检查
	sort.Slice(accountsToCheck, func(i, j int) bool {
		// 如果都没有登录过，按ID排序
		if accountsToCheck[i].LastLogin.IsZero() && accountsToCheck[j].LastLogin.IsZero() {
			return accountsToCheck[i].ID < accountsToCheck[j].ID
		}
		// 如果其中一个没有登录过，有登录记录的优先
		if accountsToCheck[i].LastLogin.IsZero() {
			return false
		}
		if accountsToCheck[j].LastLogin.IsZero() {
			return true
		}
		// 都有登录记录，最近登录的优先
		return accountsToCheck[i].LastLogin.After(accountsToCheck[j].LastLogin)
	})

	a.addLog("INFO", fmt.Sprintf("开始按最后登录时间优先检查 %d 个账号的封禁状态", len(accountsToCheck)), "bancheck")

	// 按优先级顺序检查
	for i, account := range accountsToCheck {
		a.addLog("INFO", fmt.Sprintf("检查优先级 %d/%d: 账号 %s (最后登录: %s)",
			i+1, len(accountsToCheck), account.Username,
			formatLastLogin(account.LastLogin)), "bancheck")

		go a.checkSingleAccountBan(account.ID, account.SteamID)
		time.Sleep(2 * time.Second) // 增加间隔避免API限制
	}
}

// checkBanStatusOnStartup 启动时检测封禁状态
func (a *App) checkBanStatusOnStartup() {
	if len(a.config.API.SteamAPIKeys) == 0 {
		a.addLog("WARN", "未配置Steam API密钥，无法检测封禁状态", "startup")
		return
	}

	a.mutex.RLock()
	accountsToCheck := make([]Account, 0)

	// 筛选需要检查的账号：有SteamID且1小时内未检测的
	for _, account := range a.accounts {
		if account.SteamID != "" && account.SteamID != "FAILED" {
			// 检查是否1小时内未检测
			if account.LastChecked.IsZero() || time.Since(account.LastChecked) > time.Hour {
				accountsToCheck = append(accountsToCheck, account)
			}
		}
	}
	a.mutex.RUnlock()

	if len(accountsToCheck) == 0 {
		a.addLog("INFO", "启动检测：所有账号的封禁状态都是最新的，无需检测", "startup")
		return
	}

	// 按最后登录时间排序，最近登录的优先检查
	sort.Slice(accountsToCheck, func(i, j int) bool {
		// 如果都没有登录过，按ID排序
		if accountsToCheck[i].LastLogin.IsZero() && accountsToCheck[j].LastLogin.IsZero() {
			return accountsToCheck[i].ID < accountsToCheck[j].ID
		}
		// 如果其中一个没有登录过，有登录记录的优先
		if accountsToCheck[i].LastLogin.IsZero() {
			return false
		}
		if accountsToCheck[j].LastLogin.IsZero() {
			return true
		}
		// 都有登录记录，最近登录的优先
		return accountsToCheck[i].LastLogin.After(accountsToCheck[j].LastLogin)
	})

	a.addLog("INFO", fmt.Sprintf("启动检测：发现 %d 个账号需要检测封禁状态", len(accountsToCheck)), "startup")

	// 按优先级顺序检查
	for i, account := range accountsToCheck {
		lastCheckedText := "从未检测"
		if !account.LastChecked.IsZero() {
			lastCheckedText = formatLastLogin(account.LastChecked)
		}

		a.addLog("INFO", fmt.Sprintf("启动检测 %d/%d: 账号 %s (最后登录: %s, 最后检测: %s)",
			i+1, len(accountsToCheck), account.Username,
			formatLastLogin(account.LastLogin), lastCheckedText), "startup")

		go a.checkSingleAccountBan(account.ID, account.SteamID)
		time.Sleep(2 * time.Second) // 避免API限制
	}

	a.addLog("SUCCESS", fmt.Sprintf("启动检测：已启动 %d 个账号的封禁状态检测任务", len(accountsToCheck)), "startup")
}

// checkSingleAccountBan 检查单个账号封禁状态
func (a *App) checkSingleAccountBan(accountID int, steamID string) {
	// 获取账号用户名用于日志显示
	a.mutex.RLock()
	var username string
	for _, account := range a.accounts {
		if account.ID == accountID {
			username = account.Username
			break
		}
	}
	a.mutex.RUnlock()

	if username == "" {
		username = fmt.Sprintf("ID:%d", accountID)
	}

	a.addLog("INFO", fmt.Sprintf("开始检查账号 %s 的封禁状态 (SteamID: %s)", username, steamID), "bancheck")

	// 调用Steam API检查封禁状态
	banInfo, err := a.checkSteamBanStatus(steamID)
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("检查账号 %s 封禁状态失败: %v", username, err), "bancheck")
		return
	}

	// 确定封禁状态
	banStatus := 0 // 默认正常
	statusText := "正常"

	if banInfo.VACBanned {
		banStatus = 1
		statusText = "VAC封禁"
	} else if banInfo.NumberOfGameBans > 0 {
		banStatus = 2
		statusText = "游戏封禁"
	}

	a.addLog("SUCCESS", fmt.Sprintf("账号 %s 封禁检查完成: %s (VAC:%v, 游戏封禁数:%d)",
		username, statusText, banInfo.VACBanned, banInfo.NumberOfGameBans), "bancheck")

	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 更新账号状态
	for i, account := range a.accounts {
		if account.ID == accountID {
			a.accounts[i].PUBGBanStatus = banStatus
			a.accounts[i].LastChecked = time.Now()
			a.accounts[i].Status = statusText

			// 更新数据库
			if a.db != nil {
				_, err := a.db.Exec(`
					UPDATE accounts SET pubg_ban_status = ?, last_checked = ?, updated_at = ? WHERE id = ?
				`, banStatus, time.Now(), time.Now(), accountID)
				if err != nil {
					a.addLog("ERROR", fmt.Sprintf("更新账号 %s 封禁状态到数据库失败: %v", username, err), "bancheck")
				} else {
					a.addLog("INFO", fmt.Sprintf("账号 %s 封禁状态已保存到数据库", username), "bancheck")
				}
			}
			break
		}
	}
}

// checkSteamBanStatus 调用Steam API检查封禁状态
func (a *App) checkSteamBanStatus(steamID string) (*SteamBanInfo, error) {
	// 获取可用的API密钥
	apiKey := a.getNextAPIKey()
	if apiKey == "" {
		return nil, fmt.Errorf("没有可用的Steam API密钥")
	}

	// 构建API URL
	url := fmt.Sprintf("https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=%s&steamids=%s", apiKey, steamID)

	// 发送HTTP请求
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", "网络错误或请求频繁!")
	}

	// 解析JSON响应
	var banResponse SteamBanResponse
	if err := json.Unmarshal(body, &banResponse); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	// 检查是否有结果
	if len(banResponse.Players) == 0 {
		return nil, fmt.Errorf("未找到Steam用户: %s", steamID)
	}

	return &banResponse.Players[0], nil
}

// checkBanStatusAfterSteamID SteamID获取成功后检测封禁状态
func (a *App) checkBanStatusAfterSteamID(accountID int, steamID string) {
	// 添加短暂延迟，避免API调用过于频繁
	time.Sleep(3 * time.Second)

	// 获取账号用户名用于日志显示
	a.mutex.RLock()
	var username string
	for _, account := range a.accounts {
		if account.ID == accountID {
			username = account.Username
			break
		}
	}
	a.mutex.RUnlock()

	if username == "" {
		username = fmt.Sprintf("ID:%d", accountID)
	}

	a.addLog("INFO", fmt.Sprintf("开始自动检测账号 %s 的封禁状态", username), "bancheck")

	// 直接调用检查封禁状态的方法
	a.checkSingleAccountBan(accountID, steamID)
}

// formatLastLogin 格式化最后登录时间
func formatLastLogin(lastLogin time.Time) string {
	if lastLogin.IsZero() {
		return "从未登录"
	}

	now := time.Now()
	diff := now.Sub(lastLogin)

	if diff < time.Minute {
		return "刚刚"
	} else if diff < time.Hour {
		return fmt.Sprintf("%d分钟前", int(diff.Minutes()))
	} else if diff < 24*time.Hour {
		return fmt.Sprintf("%d小时前", int(diff.Hours()))
	} else if diff < 7*24*time.Hour {
		return fmt.Sprintf("%d天前", int(diff.Hours()/24))
	} else {
		return lastLogin.Format("2006-01-02 15:04")
	}
}
