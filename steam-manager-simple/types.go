package main

import "time"

// Account 账号结构
type Account struct {
	ID            int       `json:"id"`
	Username      string    `json:"username"`
	Password      string    `json:"password"`
	Notes         string    `json:"notes"`
	Status        string    `json:"status"`
	SteamID       string    `json:"steamId"`
	PUBGBanStatus int       `json:"pubgBanStatus"`
	LastLogin     time.Time `json:"lastLogin"`
	LastChecked   time.Time `json:"lastChecked"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	IsLoggedIn    bool      `json:"isLoggedIn"` // 新增：标记是否为当前登录账号
}

// Config 配置结构
type Config struct {
	Database struct {
		Path string `json:"path"`
	} `json:"database"`
	Steam struct {
		AutoLaunchGame bool `json:"auto_launch_game"`
		GameConfig     struct {
			GameID        string `json:"game_id"`
			GameName      string `json:"game_name"`
			LaunchArgs    string `json:"launch_args"`
			AutoCloseGame bool   `json:"auto_close_game"`
		} `json:"game_config"`
	} `json:"steam"`
	Annie struct {
		Enabled     bool   `json:"enabled"`
		RarPassword string `json:"rar_password"`
	} `json:"annie"`
	API struct {
		SteamAPIKeys []string `json:"steam_api_keys"`
	} `json:"api"`
}

// SteamGame Steam游戏信息
type SteamGame struct {
	AppID string `json:"app_id"`
	Name  string `json:"name"`
}

// SteamBanInfo Steam封禁信息
type SteamBanInfo struct {
	SteamID          string `json:"SteamId"`
	CommunityBanned  bool   `json:"CommunityBanned"`
	VACBanned        bool   `json:"VACBanned"`
	NumberOfVACBans  int    `json:"NumberOfVACBans"`
	DaysSinceLastBan int    `json:"DaysSinceLastBan"`
	NumberOfGameBans int    `json:"NumberOfGameBans"`
	EconomyBan       string `json:"EconomyBan"`
}

// SteamBanResponse Steam API响应
type SteamBanResponse struct {
	Players []SteamBanInfo `json:"players"`
}

// SteamUserInfo Steam用户信息
type SteamUserInfo struct {
	SteamID     string `json:"steamid"`
	PersonaName string `json:"personaname"`
	ProfileURL  string `json:"profileurl"`
}

// SteamUserResponse Steam用户API响应
type SteamUserResponse struct {
	Response struct {
		Players []SteamUserInfo `json:"players"`
	} `json:"response"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp string `json:"timestamp"`
	Level     string `json:"level"`
	Message   string `json:"message"`
	Category  string `json:"category"` // steamid, bancheck, general
}

// AccountListResponse 账号列表响应
type AccountListResponse struct {
	Accounts    []Account `json:"accounts"`
	Total       int       `json:"total"`
	CurrentPage int       `json:"currentPage"`
	TotalPages  int       `json:"totalPages"`
	PageSize    int       `json:"pageSize"`
}

// ImportAccount 导入账号结构
type ImportAccount struct {
	Username string
	Password string
}
